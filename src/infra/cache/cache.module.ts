import KeyvRedis from '@keyv/redis';
import { CacheModule as CacheDefaultModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';

import configuration from '@/infra/configuration';
import { EventHelperModule } from '@/infra/eventHelper/eventHelper.module';

import { CacheController } from './cache.controller';
import { CacheService } from './cache.service';
import { ExtendedCacheService } from './extended-cache.service';

const { CACHE_DEFAULT_TTL, REDIS_APP_NAMESPACE, REDIS_KEY_SEPARATOR, REDIS_HOST, REDIS_PORT, REDIS_DB } = configuration;

// TODO Add global
@Module({
	imports: [
		CacheDefaultModule.registerAsync({
			useFactory: async () => {
				const redisUri = `redis://${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}`;
				return {
					isGlobal: true,
					stores: [
						// Default store for cache-manager
						new KeyvRedis(redisUri),
					],
					ttl: CACHE_DEFAULT_TTL,
				};
			},
		}),
		EventHelperModule,
	],
	providers: [CacheService, ExtendedCacheService],
	exports: [CacheService, ExtendedCacheService],
	controllers: [CacheController],
})
export class CacheModule {}
