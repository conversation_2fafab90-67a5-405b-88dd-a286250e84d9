import { RedisClientType } from 'redis';
import * as RedisLock from 'redis-lock';

type RedisLock = (key: string, timeout?: number) => Promise<() => Promise<void>>;

export class RedisLockManager {
	private readonly lockFn: RedisLock;

	constructor(private client: RedisClientType, private retryDelay?: number) {
		this.lockFn = RedisLock(client, retryDelay);
	}

	public async withLock<T>(key: string, timeout: number, fn: () => Promise<T>): Promise<T> {
		const release = await this.lockFn(key, timeout);
		try {
			return await fn();
		} catch (e) {
			throw e;
		} finally {
			await release();
		}
	}
}
