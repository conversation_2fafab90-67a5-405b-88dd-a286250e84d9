import * as assert from 'node:assert';

import { RedisClientType } from 'redis';
import * as _ from 'lodash';
import { Search, Schema, Repository, SchemaDefinition, EntityData, Client } from 'redis-om';

import configuration from '@/infra/configuration';
import { RedisLockManager } from '@/infra/redis/utils/lockManager';

import {
	DEFAULT_ENTRIES_TTL,
	DEFAULT_RELEVANCY_THRESHOLD,
	HSET_BATCH_SIZE,
	DELETE_BATCH_SIZE,
	REDIS_INDEX_LOCK_TIMEOUT,
	REDIS_ENTRIES_LOCK_TIMEOUT,
} from '../constants';

type AliasRecord = {
	groupKey: string;
	aliases: string[];
};

export type UpdateCallback<Entry> = () => Promise<[entries: Entry[], aliases: string[]]>;
export { Search };

const { REDIS_APP_NAMESPACE, REDIS_KEY_SEPARATOR } = configuration;

export class RedisSearchManager<Entry extends EntityData> {
	private readonly _indexName: string;
	private readonly _lockKey: string;
	private readonly _indexEntriesPrefix: string;
	private readonly _indexAliasesPrefix: string;
	private readonly _repository: Repository<Entry>;

	constructor(
		protected readonly entityName: string,
		protected readonly schemaDefinition: SchemaDefinition<Entry>,
		protected readonly redisClient: RedisClientType,
		protected readonly redisLock: RedisLockManager,
		protected readonly entriesTTL: number = DEFAULT_ENTRIES_TTL,
		protected readonly relevancyThreshold: number = DEFAULT_RELEVANCY_THRESHOLD,
	) {
		// 1. Creating the index name in a format like "esw:indices:clubs"
		this._indexName = [REDIS_APP_NAMESPACE, 'indices', entityName].join(REDIS_KEY_SEPARATOR);
		// 2. Creating the index entries prefix in a format like "esw:indices:clubs:"
		this._indexEntriesPrefix = this._indexName + REDIS_KEY_SEPARATOR;
		// 3. Creating the index aliases prefix in a format like "esw:indices:clubs:alias:"
		this._indexAliasesPrefix = `${this._indexEntriesPrefix}aliases${REDIS_KEY_SEPARATOR}`;
		// 4. Creating the repository to perform the index creation and search operations
		const schema = new Schema(this._indexName, schemaDefinition, { indexName: this._indexName, dataStructure: 'HASH' });
		// Create a redis-om compatible client by casting the type
		this._repository = new Repository(schema, this.redisClient as any);
		this._applyDropIndexPatch();

		this._lockKey = [REDIS_APP_NAMESPACE, 'index', entityName].join(REDIS_KEY_SEPARATOR);
	}

	sanitizeSearchText(text: string): string {
		// Removing special/formatting characters from the text to avoid issues with the Redis search
		if (!text) return '';
		return text.replace(/[@<>{}\[\]\(\)\":%!\.\?\*\+\\\-\|'`]/g, ' ').trim();
	}

	find(): Search<Entry> {
		return this._repository.search();
	}

	createOrUpdateIndex(): Promise<void> {
		return this.redisLock.withLock(this._lockKey, REDIS_INDEX_LOCK_TIMEOUT, () => this._repository.createIndex());
	}

	async actualizeIndexEntries(alias: string, updateCallback: UpdateCallback<Entry>): Promise<void> {
		// Verify if the index entries are still relevant using the alias
		if (await this.indexEntriesAreRelevant(alias)) {
			return; // Do nothing if the index entries are still relevant
		}
		// Update the index entries
		await this.redisLock.withLock(this._lockKey, REDIS_ENTRIES_LOCK_TIMEOUT, async () => {
			// Check if the index entries weren't updated while waiting for the lock
			if (await this.indexEntriesAreRelevant(alias)) return;

			const [entries, aliases] = await updateCallback();
			const aliasRecord = this._createIndexAliasRecord(aliases);
			await this._setIndexEntries(entries, aliasRecord.groupKey);
			await this._setIndexAliasRecord(aliasRecord);
		});
	}

	async indexEntriesAreRelevant(alias: string): Promise<boolean> {
		// Checking if index entries are still relevant. The relevancy is determined by the presence of the alias key
		return !!(await this.redisClient.EXISTS(this._getIndexAliasKey(alias)));
	}

	clearIndexEntries(alias: string): Promise<void> {
		return this.redisLock.withLock(this._lockKey, REDIS_ENTRIES_LOCK_TIMEOUT, async () => {
			const aliasRecord = await this._getIndexAliasRecord(alias);
			if (!aliasRecord) return;

			await this._clearIndexAliases(aliasRecord.aliases); // Deleting the aliases first (to invalidate the relevancy)
			await this._unlinkIndexEntries(aliasRecord.groupKey);
		});
	}

	private _getIndexAliasKey(alias: string): string {
		return `${this._indexAliasesPrefix}${alias}`;
	}

	private async _getIndexAliasRecord(alias: string): Promise<AliasRecord | null> {
		const aliasValue = await this.redisClient.GET(this._getIndexAliasKey(alias));
		return aliasValue ? JSON.parse(aliasValue) : null;
	}

	private async _setIndexAliasRecord(aliasRecord: AliasRecord): Promise<void> {
		const aliasValue = JSON.stringify(aliasRecord);
		const pipeline = this.redisClient.MULTI();
		aliasRecord.aliases.forEach((alias) => pipeline.PSETEX(this._getIndexAliasKey(alias), this.entriesTTL, aliasValue));
		await pipeline.EXEC();
	}

	private async _clearIndexAliases(aliases: string[]): Promise<void> {
		await this.redisClient.DEL(aliases.map((alias) => this._getIndexAliasKey(alias)));
	}

	private _createIndexAliasRecord(aliases: string[]): AliasRecord {
		assert(aliases.length, 'At least one alias should be provided');
		const [groupKey] = aliases; // Taking the first alias as the group key
		return { groupKey, aliases };
	}

	private _getIndexEntryKeyPrefix(groupKey: string): string {
		// Creating the entries key prefix in a format esw:indices:clubs:${group_key}:
		return `${this._indexEntriesPrefix}${groupKey}${REDIS_KEY_SEPARATOR}`;
	}

	private async _setIndexEntries(entries: Entry[], groupKey: string): Promise<void> {
		const entryKeyPrefix = this._getIndexEntryKeyPrefix(groupKey);
		// The entries are kept for the entriesTTL + relevancyThreshold time.
		// This will ensure that the aliases expire first making the entries irrelevant, but still keeping the entries for a while.
		const ttl = this.entriesTTL + this.relevancyThreshold;
		let index = 0;
		await Promise.all(
			_.chunk(entries, HSET_BATCH_SIZE).map((chunk) => {
				const pipeline = this.redisClient.MULTI();
				chunk.forEach((entry) => {
					const key = `${entryKeyPrefix}${index++}`;
					pipeline.HSET(key, entry as Record<string, string>);
					pipeline.PEXPIRE(key, ttl);
				});
				return pipeline.EXEC();
			}),
		);
	}

	private async _unlinkIndexEntries(groupKey: string): Promise<void> {
		const keys = await this.redisClient.KEYS(`${this._getIndexEntryKeyPrefix(groupKey)}*`);
		await this._unlinkKeys(keys);
	}

	private async _unlinkKeys(keys: string[]): Promise<void> {
		await Promise.all(_.chunk(keys, DELETE_BATCH_SIZE).map((chunk) => this.redisClient.UNLINK(chunk)));
	}

	private _applyDropIndexPatch() {
		const client = (this._repository as unknown as Record<string, Client>).client;
		const aliasesPrefix = this._indexAliasesPrefix;

		client.dropIndex = async function (indexName: string) {
			// 1. Ensure all index entries are cleared
			await this.redis.ft.DROPINDEX(indexName, { DD: true });
			// 2. Ensure all index aliases are cleared
			const keys = await this.redis.KEYS(`${aliasesPrefix}*`);
			if (!keys.length) return;
			await this.redis.UNLINK(keys);
		}.bind(client);
	}
}
